import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/app_controller.dart';


class HomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Provider.of<AppController>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('Bienvenue, ${controller.userName ?? "Utilisateur"}'),
        actions: [
          IconButton(
            icon: Icon(
              controller.isDarkTheme ? Icons.light_mode : Icons.dark_mode,
            ),
            onPressed: () {
              controller.toggleTheme();
            },
          ),
        ],
      ),
      body: const Center(
        child: Text('Contenu de la page d\'accueil'),
      ),
    );
  }
}