{"logTime": "0711/100006", "correlationVector":"mH338CB7rPIt+aWN03zthC.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000z"}}
{"logTime": "0711/100006", "correlationVector":"mH338CB7rPIt+aWN03zthC.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/100008", "correlationVector":"KGeyqHrhlcP9WlmMc4ZWYW","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "0711/100008", "correlationVector":"KGeyqHrhlcP9WlmMc4ZWYW.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 3, Last key timestamp: 2025-06-15T09:00:28Z}
{"logTime": "0711/100008", "correlationVector":"KGeyqHrhlcP9WlmMc4ZWYW.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[3]:[Eb5WYkAUEqlSWRgQNGXQCw4hZ6BraUdk43P6irD2UbVUaU0D2aI4lShATO1Ile6aKVA0kmxZc35WrYRxS/3C1g==][VB6+a0FvWZ7QfZuiVoUuosxh4/5QQhkzJgdcHdcaVy3M+zJsj/FxvwY6bU9bbyQ0sOvuu+q5He38MN74cgTOGw==][EwcmTHlFMBzdyw+iYS1zshr9c3shg13KXNyrwA8+0/mO7KqeyRl6YWOrEyq3j453UVtBbOs9of/rzC4KAEgV6g==]}
{"logTime": "0711/100008", "correlationVector":"KGeyqHrhlcP9WlmMc4ZWYW.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[3]:[2024-06-18T13:45:51Z][2024-12-16T12:32:59Z][2025-06-15T09:00:28Z]}
{"logTime": "0711/100008", "correlationVector":"mH338CB7rPIt+aWN03zthC","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=mH338CB7rPIt+aWN03zthC}
{"logTime": "0711/100008", "correlationVector":"mH338CB7rPIt+aWN03zthC.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=mH338CB7rPIt+aWN03zthC.0;server=akswtt00900000z;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100008", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=7UEbxXWoHpLdKL/GSHUYG/}
{"logTime": "0711/100013", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000001"}}
{"logTime": "0711/100013", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"75", "total":"75"}}
{"logTime": "0711/100013", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"7", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"61", "total":"61"}}
{"logTime": "0711/100013", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0711/100013", "correlationVector":"7UEbxXWoHpLdKL/GSHUYG/.5","action":"GetUpdates Response", "result":"Success", "context":Received 138 update(s). cV=7UEbxXWoHpLdKL/GSHUYG/.0;server=akswtt009000001;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100013", "correlationVector":"njZAO/hx/uF4QPR2SZVIQa","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=njZAO/hx/uF4QPR2SZVIQa}
{"logTime": "0711/100014", "correlationVector":"njZAO/hx/uF4QPR2SZVIQa.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000049"}}
{"logTime": "0711/100014", "correlationVector":"njZAO/hx/uF4QPR2SZVIQa.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Bookmarks", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"34", "total":"34"}}
{"logTime": "0711/100014", "correlationVector":"njZAO/hx/uF4QPR2SZVIQa.3","action":"GetUpdates Response", "result":"Success", "context":Received 34 update(s). cV=njZAO/hx/uF4QPR2SZVIQa.0;server=akswtt009000049;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100014", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=vAFPHKaaMXgiFO6w+0VzRx}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900002w"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"239", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"239", "total":"239"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Web Apps", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Saved Tab Group", "deleted":"3", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0711/100015", "correlationVector":"vAFPHKaaMXgiFO6w+0VzRx.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=vAFPHKaaMXgiFO6w+0VzRx.0;server=akswtt00900002w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100015", "correlationVector":"f4pRwYiQeA6KwnA87yU4GG","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=f4pRwYiQeA6KwnA87yU4GG}
{"logTime": "0711/100017", "correlationVector":"f4pRwYiQeA6KwnA87yU4GG.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000001"}}
{"logTime": "0711/100017", "correlationVector":"f4pRwYiQeA6KwnA87yU4GG.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"247", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"247", "total":"247"}}
{"logTime": "0711/100017", "correlationVector":"f4pRwYiQeA6KwnA87yU4GG.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0711/100017", "correlationVector":"f4pRwYiQeA6KwnA87yU4GG.4","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=f4pRwYiQeA6KwnA87yU4GG.0;server=akswtt009000001;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100017", "correlationVector":"0a9353+L4zFDa4BGos0NhZ","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=0a9353+L4zFDa4BGos0NhZ}
{"logTime": "0711/100018", "correlationVector":"0a9353+L4zFDa4BGos0NhZ.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000x"}}
{"logTime": "0711/100018", "correlationVector":"0a9353+L4zFDa4BGos0NhZ.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"61", "total":"61"}}
{"logTime": "0711/100018", "correlationVector":"0a9353+L4zFDa4BGos0NhZ.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"72", "total":"72"}}
{"logTime": "0711/100018", "correlationVector":"0a9353+L4zFDa4BGos0NhZ.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"117", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"117", "total":"117"}}
{"logTime": "0711/100018", "correlationVector":"0a9353+L4zFDa4BGos0NhZ.5","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=0a9353+L4zFDa4BGos0NhZ.0;server=akswtt00900000x;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100018", "correlationVector":"tZPGaXYHw1qlqJA0gNVUpt","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=tZPGaXYHw1qlqJA0gNVUpt}
{"logTime": "0711/100019", "correlationVector":"tZPGaXYHw1qlqJA0gNVUpt.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900003h"}}
{"logTime": "0711/100019", "correlationVector":"tZPGaXYHw1qlqJA0gNVUpt.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"218", "total":"218"}}
{"logTime": "0711/100019", "correlationVector":"tZPGaXYHw1qlqJA0gNVUpt.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"32", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"32", "total":"32"}}
{"logTime": "0711/100019", "correlationVector":"tZPGaXYHw1qlqJA0gNVUpt.4","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=tZPGaXYHw1qlqJA0gNVUpt.0;server=akswtt00900003h;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=9vHzdhMsqy9OjxpObYlQM7}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000039"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"149", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"156", "total":"156"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Saved Tab Group", "deleted":"3", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"5", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"7", "total":"7"}}
{"logTime": "0711/100019", "correlationVector":"9vHzdhMsqy9OjxpObYlQM7.8","action":"GetUpdates Response", "result":"Success", "context":Received 175 update(s). cV=9vHzdhMsqy9OjxpObYlQM7.0;server=akswtt009000039;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100019", "correlationVector":"mx3+KcqdvxFHugNbC3M1ZO","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=mx3+KcqdvxFHugNbC3M1ZO}
{"logTime": "0711/100020", "correlationVector":"mx3+KcqdvxFHugNbC3M1ZO.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000038"}}
{"logTime": "0711/100020", "correlationVector":"mx3+KcqdvxFHugNbC3M1ZO.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100020", "correlationVector":"mx3+KcqdvxFHugNbC3M1ZO.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=mx3+KcqdvxFHugNbC3M1ZO.0;server=akswtt009000038;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100020", "correlationVector":"Tqy0JyryY5FFiKYVKC+WL5","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=Tqy0JyryY5FFiKYVKC+WL5}
{"logTime": "0711/100021", "correlationVector":"Tqy0JyryY5FFiKYVKC+WL5.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000038"}}
{"logTime": "0711/100021", "correlationVector":"Tqy0JyryY5FFiKYVKC+WL5.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100021", "correlationVector":"Tqy0JyryY5FFiKYVKC+WL5.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=Tqy0JyryY5FFiKYVKC+WL5.0;server=akswtt009000038;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100021", "correlationVector":"BKIB/aiMmK0lTvNot096D2","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=BKIB/aiMmK0lTvNot096D2}
{"logTime": "0711/100022", "correlationVector":"BKIB/aiMmK0lTvNot096D2.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000001"}}
{"logTime": "0711/100022", "correlationVector":"BKIB/aiMmK0lTvNot096D2.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100022", "correlationVector":"BKIB/aiMmK0lTvNot096D2.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=BKIB/aiMmK0lTvNot096D2.0;server=akswtt009000001;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100022", "correlationVector":"mQzBW4gBUOug+pw25bRCIn","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=mQzBW4gBUOug+pw25bRCIn}
{"logTime": "0711/100023", "correlationVector":"mQzBW4gBUOug+pw25bRCIn.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000w"}}
{"logTime": "0711/100023", "correlationVector":"mQzBW4gBUOug+pw25bRCIn.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100023", "correlationVector":"mQzBW4gBUOug+pw25bRCIn.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=mQzBW4gBUOug+pw25bRCIn.0;server=akswtt00900000w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100023", "correlationVector":"MX6BqaE89JvVEq8Y2shexH","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=MX6BqaE89JvVEq8Y2shexH}
{"logTime": "0711/100024", "correlationVector":"MX6BqaE89JvVEq8Y2shexH.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000039"}}
{"logTime": "0711/100024", "correlationVector":"MX6BqaE89JvVEq8Y2shexH.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100024", "correlationVector":"MX6BqaE89JvVEq8Y2shexH.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=MX6BqaE89JvVEq8Y2shexH.0;server=akswtt009000039;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100024", "correlationVector":"MsVWfg++cEYwpP9qnJGLwj","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=MsVWfg++cEYwpP9qnJGLwj}
{"logTime": "0711/100025", "correlationVector":"MsVWfg++cEYwpP9qnJGLwj.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900002w"}}
{"logTime": "0711/100025", "correlationVector":"MsVWfg++cEYwpP9qnJGLwj.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100025", "correlationVector":"MsVWfg++cEYwpP9qnJGLwj.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=MsVWfg++cEYwpP9qnJGLwj.0;server=akswtt00900002w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100025", "correlationVector":"DolGjzSv5fkXlg6TQAe/A9","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=DolGjzSv5fkXlg6TQAe/A9}
{"logTime": "0711/100025", "correlationVector":"DolGjzSv5fkXlg6TQAe/A9.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000049"}}
{"logTime": "0711/100025", "correlationVector":"DolGjzSv5fkXlg6TQAe/A9.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100025", "correlationVector":"DolGjzSv5fkXlg6TQAe/A9.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=DolGjzSv5fkXlg6TQAe/A9.0;server=akswtt009000049;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100025", "correlationVector":"1F68UvHYumCCzZAGGHSiAF","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=1F68UvHYumCCzZAGGHSiAF}
{"logTime": "0711/100026", "correlationVector":"1F68UvHYumCCzZAGGHSiAF.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000y"}}
{"logTime": "0711/100026", "correlationVector":"1F68UvHYumCCzZAGGHSiAF.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100026", "correlationVector":"1F68UvHYumCCzZAGGHSiAF.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=1F68UvHYumCCzZAGGHSiAF.0;server=akswtt00900000y;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100026", "correlationVector":"g9v4eA9mxVWMIsVD7g72tq","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=g9v4eA9mxVWMIsVD7g72tq}
{"logTime": "0711/100027", "correlationVector":"g9v4eA9mxVWMIsVD7g72tq.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000026"}}
{"logTime": "0711/100027", "correlationVector":"g9v4eA9mxVWMIsVD7g72tq.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100027", "correlationVector":"g9v4eA9mxVWMIsVD7g72tq.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=g9v4eA9mxVWMIsVD7g72tq.0;server=akswtt009000026;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100027", "correlationVector":"7U5QJ+rNqNWfrdzmrwg/cU","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=7U5QJ+rNqNWfrdzmrwg/cU}
{"logTime": "0711/100028", "correlationVector":"7U5QJ+rNqNWfrdzmrwg/cU.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000y"}}
{"logTime": "0711/100028", "correlationVector":"7U5QJ+rNqNWfrdzmrwg/cU.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100028", "correlationVector":"7U5QJ+rNqNWfrdzmrwg/cU.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=7U5QJ+rNqNWfrdzmrwg/cU.0;server=akswtt00900000y;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100028", "correlationVector":"LPewT5dF+xd9xfiGhapmp0","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=LPewT5dF+xd9xfiGhapmp0}
{"logTime": "0711/100029", "correlationVector":"LPewT5dF+xd9xfiGhapmp0.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900003c"}}
{"logTime": "0711/100029", "correlationVector":"LPewT5dF+xd9xfiGhapmp0.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100029", "correlationVector":"LPewT5dF+xd9xfiGhapmp0.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=LPewT5dF+xd9xfiGhapmp0.0;server=akswtt00900003c;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100029", "correlationVector":"7KnN0Ro+TTqKI/KA3IdC+d","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=7KnN0Ro+TTqKI/KA3IdC+d}
{"logTime": "0711/100029", "correlationVector":"7KnN0Ro+TTqKI/KA3IdC+d.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900005l"}}
{"logTime": "0711/100029", "correlationVector":"7KnN0Ro+TTqKI/KA3IdC+d.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100029", "correlationVector":"7KnN0Ro+TTqKI/KA3IdC+d.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=7KnN0Ro+TTqKI/KA3IdC+d.0;server=akswtt00900005l;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100029", "correlationVector":"SPL7iPORAziE8a1Ns320P4","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=SPL7iPORAziE8a1Ns320P4}
{"logTime": "0711/100030", "correlationVector":"SPL7iPORAziE8a1Ns320P4.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000w"}}
{"logTime": "0711/100030", "correlationVector":"SPL7iPORAziE8a1Ns320P4.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100030", "correlationVector":"SPL7iPORAziE8a1Ns320P4.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=SPL7iPORAziE8a1Ns320P4.0;server=akswtt00900000w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100030", "correlationVector":"CCFwramqoV/1l2GP7HkwH0","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=CCFwramqoV/1l2GP7HkwH0}
{"logTime": "0711/100031", "correlationVector":"CCFwramqoV/1l2GP7HkwH0.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900005k"}}
{"logTime": "0711/100031", "correlationVector":"CCFwramqoV/1l2GP7HkwH0.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100031", "correlationVector":"CCFwramqoV/1l2GP7HkwH0.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=CCFwramqoV/1l2GP7HkwH0.0;server=akswtt00900005k;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100031", "correlationVector":"tMSzN6i1QInhfYRsMfS2Ei","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=tMSzN6i1QInhfYRsMfS2Ei}
{"logTime": "0711/100032", "correlationVector":"tMSzN6i1QInhfYRsMfS2Ei.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000039"}}
{"logTime": "0711/100032", "correlationVector":"tMSzN6i1QInhfYRsMfS2Ei.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100032", "correlationVector":"tMSzN6i1QInhfYRsMfS2Ei.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=tMSzN6i1QInhfYRsMfS2Ei.0;server=akswtt009000039;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100032", "correlationVector":"o4xc8fraNH/ea0GPr79nd1","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=o4xc8fraNH/ea0GPr79nd1}
{"logTime": "0711/100033", "correlationVector":"o4xc8fraNH/ea0GPr79nd1.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900005l"}}
{"logTime": "0711/100033", "correlationVector":"o4xc8fraNH/ea0GPr79nd1.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100033", "correlationVector":"o4xc8fraNH/ea0GPr79nd1.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=o4xc8fraNH/ea0GPr79nd1.0;server=akswtt00900005l;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100033", "correlationVector":"YuH9pz1Y4rQkqwrwkQOA9G","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=YuH9pz1Y4rQkqwrwkQOA9G}
{"logTime": "0711/100034", "correlationVector":"YuH9pz1Y4rQkqwrwkQOA9G.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000w"}}
{"logTime": "0711/100034", "correlationVector":"YuH9pz1Y4rQkqwrwkQOA9G.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100034", "correlationVector":"YuH9pz1Y4rQkqwrwkQOA9G.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=YuH9pz1Y4rQkqwrwkQOA9G.0;server=akswtt00900000w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100034", "correlationVector":"5z5WTF/mTG9GRfUZD6azDX","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=5z5WTF/mTG9GRfUZD6azDX}
{"logTime": "0711/100035", "correlationVector":"5z5WTF/mTG9GRfUZD6azDX.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000038"}}
{"logTime": "0711/100035", "correlationVector":"5z5WTF/mTG9GRfUZD6azDX.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100035", "correlationVector":"5z5WTF/mTG9GRfUZD6azDX.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=5z5WTF/mTG9GRfUZD6azDX.0;server=akswtt009000038;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100035", "correlationVector":"sIkF/sQdzVUfi4o2p+Nyim","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=sIkF/sQdzVUfi4o2p+Nyim}
{"logTime": "0711/100036", "correlationVector":"sIkF/sQdzVUfi4o2p+Nyim.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900006h"}}
{"logTime": "0711/100036", "correlationVector":"sIkF/sQdzVUfi4o2p+Nyim.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "0711/100036", "correlationVector":"sIkF/sQdzVUfi4o2p+Nyim.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=sIkF/sQdzVUfi4o2p+Nyim.0;server=akswtt00900006h;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted Some updates remain.}
{"logTime": "0711/100036", "correlationVector":"zA9qrtRrhyL1F9wnppGIQI","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=zA9qrtRrhyL1F9wnppGIQI}
{"logTime": "0711/100037", "correlationVector":"zA9qrtRrhyL1F9wnppGIQI.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900000w"}}
{"logTime": "0711/100037", "correlationVector":"zA9qrtRrhyL1F9wnppGIQI.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"210", "total":"210"}}
{"logTime": "0711/100037", "correlationVector":"zA9qrtRrhyL1F9wnppGIQI.3","action":"GetUpdates Response", "result":"Success", "context":Received 210 update(s). cV=zA9qrtRrhyL1F9wnppGIQI.0;server=akswtt00900000w;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100037", "correlationVector":"1++MBUxm5kXTmvX9pJCbJf","action":"Normal GetUpdate request", "result":"", "context":cV=1++MBUxm5kXTmvX9pJCbJf
Nudged types: Sessions, Device Info, User Consents
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys}
{"logTime": "0711/100037", "correlationVector":"1++MBUxm5kXTmvX9pJCbJf.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900001b"}}
{"logTime": "0711/100037", "correlationVector":"1++MBUxm5kXTmvX9pJCbJf.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0711/100037", "correlationVector":"1++MBUxm5kXTmvX9pJCbJf.3","action":"GetUpdates Response", "result":"Success", "context":Received 2 update(s). cV=1++MBUxm5kXTmvX9pJCbJf.0;server=akswtt00900001b;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP","action":"DoCompareDataConsistency requsted types: ", "result":"Bookmarks, Preferences, Passwords, Extensions, Extension settings, History Delete Directives, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Wallet, Encryption Keys"}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.0","action":"CompareDataConsistency result: ", "result":"Compare result: Encryption Keys is consistent. local entities count is: 1 local entities hash is: Pniue9pi3+yduDwaymMvvdeXnxs="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.1","action":"CompareDataConsistency result: ", "result":"Compare result: Bookmarks is consistent. local entities count is: 28 local entities hash is: JeVCiwbdSOknDJzuJPOXQSGFYVg="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.2","action":"CompareDataConsistency result: ", "result":"Compare result: Preferences is consistent. local entities count is: 74 local entities hash is: fVR0kuyGmQHa8XH097ALud4Fz5A="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.3","action":"CompareDataConsistency result: ", "result":"Compare result: Extensions is consistent. local entities count is: 4 local entities hash is: xHfyP6ywYywA772ELHQIDLfSeYM="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.4","action":"CompareDataConsistency result: ", "result":"Compare result: Extension settings is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.5","action":"CompareDataConsistency result: ", "result":"Compare result: History Delete Directives is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.6","action":"CompareDataConsistency result: ", "result":"Server did not send this Send Tab To Self local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.7","action":"CompareDataConsistency result: ", "result":"Compare result: Web Apps is consistent. local entities count is: 1 local entities hash is: dp1ERbQ9nIk7aAkV1bRt18qFLzU="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.8","action":"CompareDataConsistency result: ", "result":"Server did not send this History local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.9","action":"CompareDataConsistency result: ", "result":"Compare result: Saved Tab Group is consistent. local entities count is: 2 local entities hash is: rHMshfLd6A0d7ZFS7sR1GzEUY0g="}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.10","action":"CompareDataConsistency result: ", "result":"Server did not send this WebAuthn Credentials local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.11","action":"CompareDataConsistency result: ", "result":"Compare result: Collection is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.12","action":"CompareDataConsistency result: ", "result":"Compare result: Edge E Drop is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.13","action":"CompareDataConsistency result: ", "result":"Compare result: Edge Wallet is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "0711/100038", "correlationVector":"IzSHcYQiUolwAXc+8oqhKP.14","action":"CompareDataConsistency result: ", "result":"Compare result: Passwords is consistent. local entities count is: 54 local entities hash is: hz+UblmVL4y2FJgAc7+NwxSbmWo="}
{"logTime": "0711/100038", "correlationVector":"6Jo1JdmpubAFaP13FyJ4l4","action":"Commit Request", "result":"", "context":Item count: 5
Contributing types: Sessions, Device Info, User Consents}
{"logTime": "0711/100038", "correlationVector":"6Jo1JdmpubAFaP13FyJ4l4.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000026"}}
{"logTime": "0711/100038", "correlationVector":"6Jo1JdmpubAFaP13FyJ4l4.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=6Jo1JdmpubAFaP13FyJ4l4.0;server=akswtt009000026;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/100038", "correlationVector":"","action":"DIAGNOSTIC_REQUEST|v1/diagnosticData/Diagnostic.SendCheckResult()|SUCCESS", "result":""}
{"logTime": "0711/103701", "correlationVector":"bP6lsPsXgbJFf6/dzZWP4f.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900002m"}}
{"logTime": "0711/103701", "correlationVector":"bP6lsPsXgbJFf6/dzZWP4f.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/103702", "correlationVector":"7ro2UAUw2ABl9Ssx99HqdO","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "0711/103702", "correlationVector":"7ro2UAUw2ABl9Ssx99HqdO.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 3, Last key timestamp: 2025-06-15T09:00:28Z}
{"logTime": "0711/103702", "correlationVector":"7ro2UAUw2ABl9Ssx99HqdO.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[3]:[Eb5WYkAUEqlSWRgQNGXQCw4hZ6BraUdk43P6irD2UbVUaU0D2aI4lShATO1Ile6aKVA0kmxZc35WrYRxS/3C1g==][VB6+a0FvWZ7QfZuiVoUuosxh4/5QQhkzJgdcHdcaVy3M+zJsj/FxvwY6bU9bbyQ0sOvuu+q5He38MN74cgTOGw==][EwcmTHlFMBzdyw+iYS1zshr9c3shg13KXNyrwA8+0/mO7KqeyRl6YWOrEyq3j453UVtBbOs9of/rzC4KAEgV6g==]}
{"logTime": "0711/103702", "correlationVector":"7ro2UAUw2ABl9Ssx99HqdO.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[3]:[2024-06-18T13:45:51Z][2024-12-16T12:32:59Z][2025-06-15T09:00:28Z]}
{"logTime": "0711/103702", "correlationVector":"bP6lsPsXgbJFf6/dzZWP4f","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=bP6lsPsXgbJFf6/dzZWP4f}
{"logTime": "0711/103702", "correlationVector":"bP6lsPsXgbJFf6/dzZWP4f.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=bP6lsPsXgbJFf6/dzZWP4f.0;server=akswtt00900002m;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/103702", "correlationVector":"wt6aEhXGdTGd/3bsRWfVxA","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=wt6aEhXGdTGd/3bsRWfVxA}
{"logTime": "0711/103706", "correlationVector":"wt6aEhXGdTGd/3bsRWfVxA.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000025"}}
{"logTime": "0711/103706", "correlationVector":"wt6aEhXGdTGd/3bsRWfVxA.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"7", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"61", "total":"61"}}
{"logTime": "0711/103706", "correlationVector":"wt6aEhXGdTGd/3bsRWfVxA.3","action":"GetUpdates Response", "result":"Success", "context":Received 61 update(s). cV=wt6aEhXGdTGd/3bsRWfVxA.0;server=akswtt009000025;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/103706", "correlationVector":"pWiv64/ocxLR73J/BOhsuH","action":"Normal GetUpdate request", "result":"", "context":cV=pWiv64/ocxLR73J/BOhsuH
Nudged types: Sessions, History
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000038"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"6", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"12", "total":"12"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"22", "total":"22"}}
{"logTime": "0711/103709", "correlationVector":"pWiv64/ocxLR73J/BOhsuH.7","action":"GetUpdates Response", "result":"Success", "context":Received 39 update(s). cV=pWiv64/ocxLR73J/BOhsuH.0;server=akswtt009000038;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/103709", "correlationVector":"a6Q66VcGUA2IVuMAwW8SNS","action":"Commit Request", "result":"", "context":Item count: 5
Contributing types: Sessions, History}
{"logTime": "0711/103709", "correlationVector":"a6Q66VcGUA2IVuMAwW8SNS.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt00900003n"}}
{"logTime": "0711/103709", "correlationVector":"a6Q66VcGUA2IVuMAwW8SNS.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=a6Q66VcGUA2IVuMAwW8SNS.0;server=akswtt00900003n;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
{"logTime": "0711/103709", "correlationVector":"a6Q66VcGUA2IVuMAwW8SNS.3","action":"Commit.Sessions", "result":"Success", "context":{"id":"f1515e07-13f2-4267-a83d-f0d824e81cfd", "isDeleted":"true", "size":"0", "version":"1752228036878"}}
{"logTime": "0711/103753", "correlationVector":"UVk1YqPt6Egcps4J7PYQzW","action":"Commit Request", "result":"", "context":Item count: 28
Contributing types: Passwords}
{"logTime": "0711/103757", "correlationVector":"UVk1YqPt6Egcps4J7PYQzW.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_francecentral_prod-s01-062-eur-francecentral", "migrationStage":"NotStarted", "server":"akswtt009000008"}}
{"logTime": "0711/103757", "correlationVector":"UVk1YqPt6Egcps4J7PYQzW.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=UVk1YqPt6Egcps4J7PYQzW.0;server=akswtt009000008;cloudType=Consumer;environment=Prod_francecentral_prod-s01-062-eur-francecentral;migrationStage=NotStarted}
