import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/app_controller.dart';

class RegisterPage extends StatelessWidget {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Inscription')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text<PERSON><PERSON>(
              controller: _nameController,
              decoration: const InputDecoration(labelText: 'Nom'),
            ),
            Text<PERSON><PERSON>(
              controller: _emailController,
              decoration: const InputDecoration(labelText: 'Email'),
            ),
            Text<PERSON>ield(
              controller: _passwordController,
              decoration: const InputDecoration(labelText: 'Mot de passe'),
              obscureText: true,
            ),
            const Si<PERSON><PERSON><PERSON>(height: 20),
            ElevatedButton(
              onPressed: () async {
                final controller = Provider.of<AppController>(context, listen: false);
                await controller.register(
                  _nameController.text,
                  _emailController.text,
                  _passwordController.text,
                );
                Navigator.pop(context);
              },
              child: const Text('S\'inscrire'),
            ),
          ],
        ),
      ),
    );
  }
}