import 'package:flutter/material.dart';
import '../models/database_helper.dart';
import '../models/user.dart';
import '../utils/preferences.dart';

class AppController with ChangeNotifier {
  bool _isDarkTheme = false;
  String? _userName;

  bool get isDarkTheme => _isDarkTheme;
  String? get userName => _userName;

  AppController() {
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    _isDarkTheme = await Preferences.getTheme();
    _userName = await Preferences.getUserName();
    notifyListeners();
  }

  Future<void> toggleTheme() async {
    _isDarkTheme = !_isDarkTheme;
    await Preferences.setTheme(_isDarkTheme);
    notifyListeners();
  }

  Future<bool> login(String email, String password) async {
    final user = await DatabaseHelper.instance.getUser(email, password);
    if (user != null) {
      _userName = user.name;
      await Preferences.setUserName(user.name);
      notifyListeners();
      return true;
    }
    return false;
  }

  Future<bool> register(String name, String email, String password) async {
    try {
      // Vérifier si l'email existe déjà
      final existingUser = await DatabaseHelper.instance.getUserByEmail(email);
      if (existingUser != null) {
        return false; // Email déjà utilisé
      }

      final user = User(name: name, email: email, password: password);
      await DatabaseHelper.instance.insertUser(user);
      return true; // Inscription réussie
    } catch (e) {
      print('Erreur lors de l\'inscription: $e');
      return false; // Erreur lors de l'inscription
    }
  }
}